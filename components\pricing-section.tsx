"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Check, Star, TrendingUp, Shield, Clock, DollarSign, Zap, ArrowRight } from "lucide-react"
import { useState } from "react"
import Image from "next/image"

export default function PricingSection() {
  const [selectedChallengeType, setSelectedChallengeType] = useState("1-phase")
  const [selectedCurrency, setSelectedCurrency] = useState("usd")

  const challengeTypes = [
    { 
      id: "1-phase", 
      name: "1 Phase", 
      active: true
    },
    { 
      id: "2-phase", 
      name: "2 Phase", 
      active: false
    },
    { id: "hft", name: "HFT", active: false },
    { id: "instal", name: "Instal", active: false },
    { id: "rapid", name: "Rapid (10 Days)", active: false }
  ]

  const currencies = [
    { id: "usd", name: "USD", symbol: "$", flag: "🇺🇸", logo: "/images/flags/us.png", active: true },
    { id: "eur", name: "EUR", symbol: "€", flag: "🇪🇺", logo: "/images/flags/eur.png", active: false },
    { id: "cad", name: "CAD", symbol: "C$", flag: "🇨🇦", logo: "/images/flags/cad.png", active: false },
    { id: "gbp", name: "GBP", symbol: "£", flag: "🇬🇧", logo: "/images/flags/gb.png", active: false },
    { id: "aud", name: "AUD", symbol: "A$", flag: "🇦🇺", logo: "/images/flags/au.png", active: false }
  ]

  const getAccountSizes = (currency: string) => {
    const currencyData = currencies.find(c => c.id === currency)
    const symbol = currencyData?.symbol || "$"
    
    return [
      { size: `${symbol}5,000`, price: 59 },
      { size: `${symbol}10,000`, price: 89 },
      { size: `${symbol}25,000`, price: 179 },
      { size: `${symbol}50,000`, price: 319 },
      { size: `${symbol}100,000`, price: 619 },
      { size: `${symbol}200,000`, price: 1209 }
    ]
  }

  const accountSizes = getAccountSizes(selectedCurrency)

  const tradingParameters = [
    { parameter: "Minimum Trading Days", value: "3" },
    { parameter: "Profit Target", value: "10%" },
    { parameter: "Max Daily Loss", value: "5%" },
    { parameter: "Max Total Loss", value: "6%" },
    { parameter: "Trading Leverage", value: "1:30" }
  ]

  return (
    <section className="py-24 md:py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950" />
      
      {/* Background Elements */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-indigo-500/5 to-blue-500/5 dark:from-indigo-500/10 dark:to-blue-500/10 rounded-full blur-3xl" />

      <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <Badge className="mb-8 bg-gradient-to-r from-blue-600 to-cyan-600 text-white border-0 px-6 py-3 text-sm font-semibold shadow-lg">
            <TrendingUp className="w-4 h-4 mr-2" />
            TRADING CHALLENGES
          </Badge>
          
          <h2 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight tracking-tight">
            Choose Your
            <span className="block bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-400 dark:via-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent">
              Challenge
            </span>
          </h2>
          
          <p className="text-xl md:text-2xl lg:text-3xl text-gray-600 dark:text-white/80 max-w-4xl mx-auto leading-relaxed font-light">
            Start with a small account and scale up to $200K. No time limits, 90% profit split, and free retries.
          </p>
        </div>

        {/* Challenge Type Selectors */}
        <div className="flex flex-col items-center mb-16">
          <div className="bg-white dark:bg-gray-900 rounded-2xl p-4 md:p-6 shadow-lg border border-gray-200 dark:border-gray-700 mb-8 w-full max-w-4xl">
            {/* All Challenge Options Row */}
            <div className="flex items-center justify-center gap-2 md:gap-4 mb-6 flex-wrap">
              {challengeTypes.map((type) => (
                <div key={type.id} className="flex flex-col items-center">
                  <Button
                    variant={type.active ? "default" : "ghost"}
                    className={`rounded-xl px-3 md:px-6 py-2 md:py-3 text-xs md:text-sm font-semibold transition-all duration-300 ${
                      type.active
                        ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:from-blue-700 hover:to-cyan-700"
                        : "text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => {
                      setSelectedChallengeType(type.id)
                      setSelectedCurrency("usd")
                    }}
                  >
                    {type.name}
                  </Button>
                </div>
              ))}
            </div>

            {/* Currency Options Row - Always show for all challenge types */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4 md:pt-6">
              <div className="text-center mb-3 md:mb-4">
                <span className="text-xs md:text-sm font-medium text-gray-600 dark:text-white/60">Select Currency</span>
              </div>
              <div className="flex items-center justify-center gap-1 md:gap-2 flex-wrap">
                {currencies.map((currency) => (
                  <Button
                    key={currency.id}
                    variant={currency.id === selectedCurrency ? "default" : "ghost"}
                    size="sm"
                    className={`rounded-lg px-2 md:px-4 py-1.5 md:py-2 text-xs md:text-sm font-medium transition-all duration-300 ${
                      currency.id === selectedCurrency
                        ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-md hover:from-blue-700 hover:to-cyan-700"
                        : "text-gray-500 dark:text-white/60 hover:text-gray-700 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => setSelectedCurrency(currency.id)}
                  >
                    <div className="relative w-4 h-4 md:w-5 md:h-5 mr-1 md:mr-2 rounded-full overflow-hidden">
                      <Image
                        src={currency.logo || "/placeholder.svg"}
                        alt={`${currency.name} flag`}
                        fill
                        className="object-cover"
                        sizes="20px"
                      />
                    </div>
                    <span className="hidden sm:inline">{currency.name}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
          
          <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-6 md:px-8 py-3 md:py-4 rounded-xl text-sm md:text-base font-semibold hover:scale-105 transition-all duration-300 shadow-lg">
            Get Funded
          </Button>
        </div>

        {/* Pricing Table */}
        <div className="bg-white dark:bg-gray-900 rounded-3xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700">
          <div className="overflow-x-auto">
            <table className="w-full min-w-[600px]">
              <thead>
                <tr className="bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20">
                  <th className="text-left p-4 md:p-6 font-bold text-gray-900 dark:text-white text-sm md:text-base">
                    Trading Parameters
                  </th>
                  {accountSizes.map((account, index) => (
                    <th key={index} className="text-center p-4 md:p-6 font-bold text-gray-900 dark:text-white text-sm md:text-base">
                      {account.size}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {tradingParameters.map((param, index) => (
                  <tr 
                    key={index} 
                    className={`border-b border-gray-100 dark:border-gray-800 ${
                      index % 2 === 0 ? "bg-gray-50 dark:bg-gray-800/50" : ""
                    }`}
                  >
                    <td className="p-4 md:p-6 font-semibold text-gray-900 dark:text-white text-sm md:text-base">
                      {param.parameter}
                    </td>
                    {accountSizes.map((account, accountIndex) => (
                      <td key={accountIndex} className="text-center p-4 md:p-6 text-gray-700 dark:text-white/70 text-sm md:text-base font-medium">
                        {param.value}
                      </td>
                    ))}
                  </tr>
                ))}
                <tr className="border-b border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/30">
                  <td className="p-4 md:p-6 font-bold text-gray-900 dark:text-white text-sm md:text-base">
                    Buy Now
                  </td>
                  {accountSizes.map((account, index) => (
                    <td key={index} className="text-center p-4 md:p-6">
                      <Button className="bg-white border-2 border-blue-500 text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-gray-700 px-4 md:px-6 py-2 md:py-3 rounded-xl text-sm md:text-base font-semibold transition-all duration-300 hover:scale-105 shadow-md">
                        ${account.price}
            </Button>
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Additional Features */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-12">
          <div className="text-center group">
            <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Shield className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Secure Trading</h3>
            <p className="text-gray-600 dark:text-white/70 text-base leading-relaxed">
              Your funds are protected with industry-leading security measures and regulatory compliance
            </p>
          </div>
          
          <div className="text-center group">
            <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Clock className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">No Time Limits</h3>
            <p className="text-gray-600 dark:text-white/70 text-base leading-relaxed">
              Take as long as you need to complete your challenge with flexible trading schedules
            </p>
                </div>

          <div className="text-center group">
            <div className="w-20 h-20 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <DollarSign className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">90% Profit Split</h3>
            <p className="text-gray-600 dark:text-white/70 text-base leading-relaxed">
              Keep 90% of your trading profits once funded with transparent profit sharing
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-24">
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-16 shadow-2xl">
            <h3 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Ready to Start Your Trading Journey?
            </h3>
            <p className="text-xl md:text-2xl text-gray-700 dark:text-white/70 mb-12 max-w-3xl mx-auto leading-relaxed font-light">
              Join thousands of funded traders. Choose your challenge and start trading with real capital today.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 px-10 py-6 text-xl font-semibold hover:scale-105 transition-all duration-300 hover:shadow-xl rounded-2xl">
                <TrendingUp className="w-6 h-6 mr-3" />
                Start Your Challenge
                <ArrowRight className="w-6 h-6 ml-3" />
            </Button>
              <Button variant="outline" className="border-2 border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 px-10 py-6 text-xl font-semibold rounded-2xl">
                <Zap className="w-6 h-6 mr-3" />
                Learn More
            </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
