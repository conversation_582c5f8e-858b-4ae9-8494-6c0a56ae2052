"use client"

import { But<PERSON> } from "@/components/ui/button"
import { 
  ExternalLink,
  Share2
} from "lucide-react"

export default function SocialMedia() {
  const socialPlatforms = [
    {
      id: 'facebook',
      name: 'Facebook',
      url: 'https://facebook.com',
      color: 'from-blue-600 to-blue-700',
      logo: '📘'
    },
    {
      id: 'twitter',
      name: 'Twitter/X',
      url: 'https://twitter.com',
      color: 'from-black to-gray-800',
      logo: '𝕏'
    },
    {
      id: 'instagram',
      name: 'Instagram',
      url: 'https://instagram.com',
      color: 'from-pink-500 via-red-500 to-yellow-500',
      logo: '📷'
    },
    {
      id: 'youtube',
      name: 'YouTube',
      url: 'https://youtube.com',
      color: 'from-red-600 to-red-700',
      logo: '📺'
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      url: 'https://linkedin.com',
      color: 'from-blue-700 to-blue-800',
      logo: '💼'
    },
    {
      id: 'telegram',
      name: 'Telegram',
      url: 'https://telegram.org',
      color: 'from-blue-500 to-cyan-500',
      logo: '✈️'
    },
    {
      id: 'discord',
      name: 'Discord',
      url: 'https://discord.com',
      color: 'from-indigo-600 to-purple-600',
      logo: '🎮'
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      url: 'https://tiktok.com',
      color: 'from-black via-red-500 to-cyan-400',
      logo: '🎵'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Social Media Platforms 🌐</h1>
            <p className="text-lg text-gray-700 dark:text-white/70">Connect with us on social media</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
              <Share2 className="w-5 h-5 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Social Media Platforms */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {socialPlatforms.map((platform) => (
          <div key={platform.id} className={`bg-gradient-to-br ${platform.color} backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 group`}>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="text-3xl">{platform.logo}</div>
                <div>
                  <h3 className="text-lg font-bold text-white">{platform.name}</h3>
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                className="flex-1 bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm"
                onClick={() => window.open(platform.url, '_blank')}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Visit
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
