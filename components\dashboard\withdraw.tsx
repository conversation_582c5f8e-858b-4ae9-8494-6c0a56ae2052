"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Wallet,
  DollarSign,
  CreditCard,
  Building2,
  CreditCard as PayPal,
  Bitcoin,
  ArrowRight,
  CheckCircle,
  Clock,
  AlertCircle,
} from "lucide-react"
import { useState } from "react"

export default function Withdraw() {
  const [withdrawMethod, setWithdrawMethod] = useState("bank")
  const [amount, setAmount] = useState("")

  const availableBalance = 2847.50
  const pendingWithdrawals = 1250.00

  const withdrawMethods = [
    {
      id: "bank",
      name: "Bank Transfer",
      icon: Building2,
      description: "2-5 business days",
      fee: "Free",
    },
    {
      id: "paypal",
      name: "PayPal",
      icon: PayPal,
      description: "Instant",
      fee: "$2.50",
    },
    {
      id: "crypto",
      name: "Cryptocurrency",
      icon: Bitcoin,
      description: "1-2 hours",
      fee: "Free",
    },
  ]

  const recentTransactions = [
    {
      id: "1",
      method: "Bank Transfer",
      amount: 1250.00,
      status: "completed",
      date: "2024-01-15",
      reference: "WT-2024-001",
    },
    {
      id: "2",
      method: "PayPal",
      amount: 500.00,
      status: "pending",
      date: "2024-01-14",
      reference: "WT-2024-002",
    },
    {
      id: "3",
      method: "Bank Transfer",
      amount: 750.00,
      status: "completed",
      date: "2024-01-10",
      reference: "WT-2024-003",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Withdraw Funds</h1>
        <p className="text-gray-600 dark:text-gray-300">Withdraw your profits to your preferred payment method</p>
      </div>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <Wallet className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Available Balance</h3>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">${availableBalance.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
                <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Pending Withdrawals</h3>
                <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">${pendingWithdrawals.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Withdraw Form */}
      <Card>
        <CardHeader>
          <CardTitle>New Withdrawal</CardTitle>
          <CardDescription>Choose your withdrawal method and amount</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Withdrawal Method */}
          <div className="space-y-4">
            <Label>Withdrawal Method</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {withdrawMethods.map((method) => {
                const IconComponent = method.icon
                return (
                  <Card
                    key={method.id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                      withdrawMethod === method.id
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => setWithdrawMethod(method.id)}
                  >
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-3 mb-2">
                        <IconComponent className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        <span className="font-semibold text-gray-900 dark:text-white">{method.name}</span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{method.description}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Fee: {method.fee}</p>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Amount (USD)</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="text-lg"
            />
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Available: ${availableBalance.toFixed(2)}
            </p>
          </div>

          {/* Quick Amount Buttons */}
          <div className="flex gap-2">
            {[100, 250, 500, 1000].map((quickAmount) => (
              <Button
                key={quickAmount}
                variant="outline"
                size="sm"
                onClick={() => setAmount(quickAmount.toString())}
                className="flex-1"
              >
                ${quickAmount}
              </Button>
            ))}
          </div>

          {/* Submit Button */}
          <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3">
            <Wallet className="w-4 h-4 mr-2" />
            Process Withdrawal
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Withdrawals</CardTitle>
          <CardDescription>Your recent withdrawal history</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <CreditCard className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white">{transaction.method}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{transaction.reference}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{transaction.date}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900 dark:text-white">${transaction.amount.toFixed(2)}</p>
                  <Badge className={getStatusColor(transaction.status)}>
                    {transaction.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Information */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Important Information</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• Minimum withdrawal amount: $100</li>
                <li>• Maximum withdrawal amount: $10,000 per day</li>
                <li>• Processing times may vary by payment method</li>
                <li>• All withdrawals are subject to verification</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
