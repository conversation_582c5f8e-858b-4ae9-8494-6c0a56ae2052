"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { 
  HelpCircle, 
  MessageCircle, 
  Phone, 
  Mail, 
  TrendingUp, 
  Shield, 
  Clock, 
  DollarSign,
  Users,
  Zap,
  Globe,
  Award,
  BookOpen,
  Settings,
  BarChart3,
  CreditCard,
  Smartphone,
  Monitor,
  Calendar,
  Target,
  ChevronDown,
  ChevronRight
} from "lucide-react"
import { useState } from "react"
import { useLanguage } from "@/contexts/language-context"

export default function FAQSection() {
  const { t } = useLanguage()
  const [activeTab, setActiveTab] = useState("general")
  const [openItems, setOpenItems] = useState<{ [key: string]: number[] }>({
    general: [0],
    technical: [0],
    financial: [0],
    support: [0]
  })

  const toggleItem = (category: string, index: number) => {
    setOpenItems(prev => ({
      ...prev,
      [category]: prev[category].includes(index) 
        ? prev[category].filter(i => i !== index)
        : [...prev[category], index]
    }))
  }

  const faqCategories = {
    general: [
      {
        icon: TrendingUp,
        question: "How does the evaluation process work?",
        answer: "Our evaluation process is straightforward: pass our trading challenge by meeting the profit target while staying within risk parameters. Once you pass, you'll receive funding to trade with our capital and keep up to 90% of the profits you generate.",
        category: "Process"
      },
      {
        icon: DollarSign,
        question: "What is the maximum funding available?",
        answer: "We offer funding up to $400,000. You can start with smaller account sizes like $10K, $25K, or $50K and scale up based on your performance and consistency.",
        category: "Funding"
      },
      {
        icon: Clock,
        question: "Are there any time limits for the evaluation?",
        answer: "No, we don't impose strict time limits on our evaluations. Take the time you need to trade consistently and meet the requirements. However, we do require a minimum number of trading days to ensure consistency.",
        category: "Timeline"
      },
      {
        icon: Shield,
        question: "What are the risk management rules?",
        answer: "Our risk management rules include maximum daily loss limits, maximum drawdown limits, and position sizing requirements. These rules are designed to protect both you and our capital while allowing for profitable trading opportunities.",
        category: "Risk Management"
      }
    ],
    technical: [
      {
        icon: Monitor,
        question: "What trading platforms do you support?",
        answer: "We primarily support MetaTrader 4 (MT4) and MetaTrader 5 (MT5). These platforms are industry standards and offer all the tools and features professional traders need.",
        category: "Platforms"
      },
      {
        icon: Settings,
        question: "Can I use Expert Advisors (EAs) and automated trading?",
        answer: "Yes, we allow Expert Advisors and automated trading strategies. We believe in giving traders the freedom to use the tools and strategies they're most comfortable with.",
        category: "Automation"
      },
      {
        icon: Smartphone,
        question: "Can I trade on mobile devices?",
        answer: "Absolutely! Our supported platforms (MT4/MT5) have excellent mobile apps that allow you to trade from anywhere. You can monitor your positions, place trades, and manage your account on the go.",
        category: "Mobile Trading"
      },
      {
        icon: BarChart3,
        question: "What trading instruments are available?",
        answer: "We offer a wide range of trading instruments including major and minor forex pairs, indices, commodities, and cryptocurrencies. The specific instruments available depend on your account type and funding level.",
        category: "Instruments"
      }
    ],
    financial: [
      {
        icon: CreditCard,
        question: "How do I receive my profits?",
        answer: "Profits are typically paid out monthly. Once you meet the minimum withdrawal threshold, you can request a payout through your dashboard. Payments are processed within 1-3 business days.",
        category: "Payouts"
      },
      {
        icon: Target,
        question: "What are the profit targets for evaluation?",
        answer: "Profit targets vary by account size, typically ranging from 8-12% of the account balance. The targets are realistic and achievable for skilled traders while maintaining proper risk management.",
        category: "Targets"
      },
      {
        icon: Award,
        question: "Are there any hidden fees?",
        answer: "No hidden fees! We believe in complete transparency. The only costs are the one-time evaluation fee and a small monthly subscription fee for funded accounts. All profits you generate are yours to keep.",
        category: "Fees"
      },
      {
        icon: Users,
        question: "Do you offer scaling plans?",
        answer: "Yes! We offer scaling plans that allow you to increase your account size based on consistent performance. This means you can start with a smaller account and grow your funding as you prove your trading skills.",
        category: "Scaling"
      }
    ],
    support: [
      {
        icon: MessageCircle,
        question: "What kind of support do you provide?",
        answer: "We provide 24/7 support to all our traders. This includes technical support, account management assistance, educational resources, and access to our trading community.",
        category: "Support"
      },
      {
        icon: BookOpen,
        question: "Do you offer educational resources?",
        answer: "Yes! We provide comprehensive educational resources including webinars, trading guides, market analysis, and access to our community of successful traders.",
        category: "Education"
      },
      {
        icon: Globe,
        question: "Is support available in multiple languages?",
        answer: "Yes, our support team is available in multiple languages including English, Spanish, Arabic, French, and German. We're committed to serving traders from around the world.",
        category: "Languages"
      },
      {
        icon: Zap,
        question: "How quickly can I get funded?",
        answer: "Once you successfully complete the evaluation, funding typically takes 1-3 business days. We verify your trading performance and then provide you with your funded account credentials.",
        category: "Timeline"
      }
    ]
  }

  const contactMethods = [
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Get instant answers to your questions",
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-500/10 to-cyan-500/10",
      borderColor: "border-blue-200 dark:border-blue-400/20",
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Detailed responses within 24 hours",
      color: "from-indigo-500 to-purple-500",
      bgColor: "from-indigo-500/10 to-purple-500/10",
      borderColor: "border-indigo-200 dark:border-indigo-400/20",
    },
    {
      icon: Phone,
      title: "Phone Support",
      description: "Speak directly with our experts",
      color: "from-cyan-500 to-teal-500",
      bgColor: "from-cyan-500/10 to-teal-500/10",
      borderColor: "border-cyan-200 dark:border-cyan-400/20",
    },
  ]

  return (
    <section className="py-24 md:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-50/20 to-transparent dark:via-blue-950/10" />
      <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 dark:from-indigo-500/10 dark:to-purple-500/10 rounded-full blur-3xl" />

      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16 relative">
        {/* Section Header */}
        <div className="text-center mb-20 md:mb-28">
          <Badge
            variant="outline"
            className="mb-6 text-sm font-light border-blue-200 dark:border-blue-400/20 text-blue-600 dark:text-blue-400 px-4 py-2"
          >
            <HelpCircle className="w-4 h-4 mr-2" />
            Got Questions?
          </Badge>

          <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="text-gray-900 dark:text-white">Frequently Asked</span>{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              Questions
            </span>
          </h2>

          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 max-w-3xl mx-auto leading-relaxed">
            Everything you need to know about our prop trading program, evaluation process, and getting funded.
          </p>
        </div>

        {/* FAQ Categories Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-20 md:mb-28">
          <TabsList className="grid w-full grid-cols-4 mb-12 bg-gray-100 dark:bg-gray-800 p-1 rounded-2xl">
            <TabsTrigger value="general" className="rounded-xl data-[state=active]:bg-white dark:data-[state=active]:bg-gray-900">
              <Shield className="w-4 h-4 mr-2" />
              General
            </TabsTrigger>
            <TabsTrigger value="technical" className="rounded-xl data-[state=active]:bg-white dark:data-[state=active]:bg-gray-900">
              <Settings className="w-4 h-4 mr-2" />
              Technical
            </TabsTrigger>
            <TabsTrigger value="financial" className="rounded-xl data-[state=active]:bg-white dark:data-[state=active]:bg-gray-900">
              <DollarSign className="w-4 h-4 mr-2" />
              Financial
            </TabsTrigger>
            <TabsTrigger value="support" className="rounded-xl data-[state=active]:bg-white dark:data-[state=active]:bg-gray-900">
              <MessageCircle className="w-4 h-4 mr-2" />
              Support
            </TabsTrigger>
          </TabsList>

          {Object.entries(faqCategories).map(([category, faqs]) => (
            <TabsContent key={category} value={category} className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {faqs.map((faq, index) => {
                  const IconComponent = faq.icon
                  const isOpen = openItems[category]?.includes(index)
                  
                  return (
                    <Collapsible 
                      key={index} 
                      open={isOpen} 
                      onOpenChange={() => toggleItem(category, index)}
                    >
                      <Card className="group bg-gradient-to-br from-white/80 to-gray-50/80 dark:from-gray-900/80 dark:to-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-xl transition-all duration-500 hover:scale-[1.02] overflow-hidden">
                        <CollapsibleTrigger asChild>
                          <CardHeader className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-300">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                                  <IconComponent className="w-6 h-6 text-white" />
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <Badge variant="secondary" className="text-xs font-medium">
                                      {faq.category}
                                    </Badge>
                                  </div>
                                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300 leading-tight text-left">
                                    {faq.question}
                                  </h3>
                                </div>
                              </div>
                              <div className="flex-shrink-0 ml-4">
                                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center transition-all duration-300 group-hover:scale-110">
                                  {isOpen ? (
                                    <ChevronDown className="w-4 h-4 text-white transition-transform duration-300" />
                                  ) : (
                                    <ChevronRight className="w-4 h-4 text-white transition-transform duration-300" />
                                  )}
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                        </CollapsibleTrigger>

                        <CollapsibleContent>
                          <CardContent className="px-6 pb-6 pt-0">
                            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                              <p className="text-gray-700 dark:text-white/80 leading-relaxed text-base">
                                {faq.answer}
                              </p>
                            </div>
                          </CardContent>
                        </CollapsibleContent>
                      </Card>
                    </Collapsible>
                  )
                })}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Contact Methods */}
        <div className="text-center">
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-8 md:mb-12">
            Still Have Questions?
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
            {contactMethods.map((method, index) => {
              const IconComponent = method.icon
              return (
                <Card
                  key={index}
                  className={`group bg-gradient-to-br ${method.bgColor} dark:${method.bgColor.replace("/10", "/15")} backdrop-blur-sm border ${method.borderColor} shadow-sm hover:shadow-xl transition-all duration-500 hover:scale-105 cursor-pointer`}
                >
                  <CardContent className="p-6 md:p-8 text-center">
                    <div
                      className={`w-16 h-16 mx-auto rounded-2xl bg-gradient-to-r ${method.color} flex items-center justify-center mb-6 group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg`}
                    >
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>

                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {method.title}
                    </h4>

                    <p className="text-gray-600 dark:text-white/70 leading-relaxed">{method.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
