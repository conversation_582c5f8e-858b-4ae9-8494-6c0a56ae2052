"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Key, 
  Lock, 
  Server, 
  DollarSign, 
  Monitor, 
  User, 
  Copy,
  Eye,
  EyeOff,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingUp,
  Shield,
  Settings,
  Download,
  ExternalLink
} from "lucide-react"
import { toast } from "sonner"

interface AccountCredential {
  id: string
  orderId: string
  login: string
  password: string
  server: string
  accountSize: string
  platformType: string
  accountType: string
  status: "active" | "pending" | "expired"
  createdAt: string
  lastLogin?: string
  balance?: string
  equity?: string
  margin?: string
  freeMargin?: string
}

export default function AccountDetails() {
  const [showPassword, setShowPassword] = useState<{ [key: string]: boolean }>({})
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const accountCredentials: AccountCredential[] = [
    {
      id: "1",
      orderId: "FX-2024-001",
      login: "********",
      password: "Trading@2024!",
      server: "FxThrone-Live",
      accountSize: "$10,000",
      platformType: "MetaTrader 5",
      accountType: "Standard",
      status: "active",
      createdAt: "2024-01-15",
      lastLogin: "2024-01-20 14:30",
      balance: "$10,250.50",
      equity: "$10,180.25",
      margin: "$1,020.00",
      freeMargin: "$9,160.25"
    },
    {
      id: "2",
      orderId: "FX-2024-002",
      login: "********",
      password: "Secure@2024!",
      server: "FxThrone-Live",
      accountSize: "$25,000",
      platformType: "MetaTrader 5",
      accountType: "Premium",
      status: "active",
      createdAt: "2024-01-20",
      lastLogin: "2024-01-21 09:15",
      balance: "$25,500.75",
      equity: "$25,320.40",
      margin: "$2,550.00",
      freeMargin: "$22,770.40"
    },
    {
      id: "3",
      orderId: "FX-2024-003",
      login: "********",
      password: "Demo@2024!",
      server: "FxThrone-Demo",
      accountSize: "$5,000",
      platformType: "MetaTrader 4",
      accountType: "Demo",
      status: "pending",
      createdAt: "2024-01-25"
    }
  ]

  const togglePasswordVisibility = (accountId: string) => {
    setShowPassword(prev => ({
      ...prev,
      [accountId]: !prev[accountId]
    }))
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast.success(`${label} copied to clipboard`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
      case "pending":
        return "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"
      case "expired":
        return "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
      default:
        return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case "Premium":
        return "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400"
      case "Standard":
        return "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
      case "Demo":
        return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
      default:
        return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const handleRefreshAccount = async (accountId: string) => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast.success("Account information refreshed")
    }, 2000)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Account Details</h1>
            <p className="text-lg text-gray-700 dark:text-white/70">Manage your trading account credentials and information</p>
          </div>
          <div className="flex items-center gap-3">
            <Badge className="bg-blue-600 dark:bg-blue-500 text-white">
              <Shield className="w-4 h-4 mr-1" />
              Secure Access
            </Badge>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <User className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{accountCredentials.length}</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Total Accounts</div>
              </div>
            </div>
          </div>

          <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center shadow-lg">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {accountCredentials.filter(acc => acc.status === 'active').length}
                </div>
                <div className="text-sm text-gray-600 dark:text-white/60">Active Accounts</div>
              </div>
            </div>
          </div>

          <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center shadow-lg">
                <Clock className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {accountCredentials.filter(acc => acc.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-600 dark:text-white/60">Pending Accounts</div>
              </div>
            </div>
          </div>

          <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                <DollarSign className="w-5 h-5 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">$40K+</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Total Value</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Account Credentials */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Account Credentials</h2>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            <Settings className="w-4 h-4 mr-2" />
            Manage Accounts
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {accountCredentials.map((account) => (
            <Card 
              key={account.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                selectedAccount === account.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedAccount(account.id)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                      <Key className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{account.platformType}</CardTitle>
                      <CardDescription>Order ID: {account.orderId}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(account.status)}>
                      {account.status.charAt(0).toUpperCase() + account.status.slice(1)}
                    </Badge>
                    <Badge className={getAccountTypeColor(account.accountType)}>
                      {account.accountType}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Account Size */}
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-600" />
                    <span className="font-medium text-gray-900 dark:text-white">Account Size</span>
                  </div>
                  <span className="font-bold text-green-600">{account.accountSize}</span>
                </div>

                {/* Login Credentials */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium text-gray-700 dark:text-white/70">Login ID</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(account.login, "Login ID")}
                      className="h-6 px-2 text-xs"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input
                      type={showPassword[account.id] ? "text" : "password"}
                      value={account.login}
                      readOnly
                      className="bg-gray-50 dark:bg-gray-800"
                    />
                  </div>
                </div>

                {/* Password */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium text-gray-700 dark:text-white/70">Password</Label>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => togglePasswordVisibility(account.id)}
                        className="h-6 px-2 text-xs"
                      >
                        {showPassword[account.id] ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(account.password, "Password")}
                        className="h-6 px-2 text-xs"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input
                      type={showPassword[account.id] ? "text" : "password"}
                      value={account.password}
                      readOnly
                      className="bg-gray-50 dark:bg-gray-800"
                    />
                  </div>
                </div>

                {/* Server */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium text-gray-700 dark:text-white/70">Server</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(account.server, "Server")}
                      className="h-6 px-2 text-xs"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Server className="w-4 h-4 text-blue-600" />
                    <Input
                      value={account.server}
                      readOnly
                      className="bg-gray-50 dark:bg-gray-800"
                    />
                  </div>
                </div>

                {/* Account Stats (if available) */}
                {account.balance && (
                  <div className="grid grid-cols-2 gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="text-center">
                      <div className="text-sm text-gray-600 dark:text-white/60">Balance</div>
                      <div className="font-bold text-green-600">{account.balance}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-gray-600 dark:text-white/60">Equity</div>
                      <div className="font-bold text-blue-600">{account.equity}</div>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2 pt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleRefreshAccount(account.id)}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      <RefreshCw className="w-4 h-4" />
                    )}
                    Refresh
                  </Button>
                  <Button size="sm" className="flex-1">
                    <ExternalLink className="w-4 h-4 mr-1" />
                    Connect
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border border-blue-200 dark:border-blue-400/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100">
            <AlertCircle className="w-5 h-5" />
            Important Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <p className="mb-2"><strong>How to change credentials:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Create a new challenge with a different Order ID</li>
              <li>New credentials will be generated automatically</li>
              <li>Previous account credentials will remain active until new ones are issued</li>
              <li>Contact support if you need to update existing credentials</li>
            </ul>
          </div>
          <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
            <Shield className="w-4 h-4" />
            <span>All credentials are encrypted and securely stored</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 