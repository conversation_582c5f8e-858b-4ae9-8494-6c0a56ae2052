"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Users,
  Share2,
  Copy,
  Gift,
  TrendingUp,
  DollarSign,
  UserPlus,
  Award,
  ArrowRight,
  CheckCircle,
  ExternalLink,
} from "lucide-react"
import { useState } from "react"

export default function Referral() {
  const [copied, setCopied] = useState(false)

  const referralStats = {
    totalReferrals: 12,
    activeReferrals: 8,
    totalEarnings: 1250.00,
    thisMonth: 450.00,
  }

  const referralLink = "https://ftmo.com/ref/badar123"
  const referralCode = "BADAR123"

  const recentReferrals = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "active",
      joinedDate: "2024-01-15",
      earnings: 125.00,
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "pending",
      joinedDate: "2024-01-14",
      earnings: 0.00,
    },
    {
      id: "3",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "active",
      joinedDate: "2024-01-10",
      earnings: 200.00,
    },
  ]

  const rewards = [
    {
      tier: "Bronze",
      referrals: "1-5",
      reward: "$50 per referral",
      icon: Gift,
      color: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
    },
    {
      tier: "Silver",
      referrals: "6-15",
      reward: "$75 per referral",
      icon: Award,
      color: "text-gray-600",
      bgColor: "bg-gray-100 dark:bg-gray-900/30",
    },
    {
      tier: "Gold",
      referrals: "16+",
      reward: "$100 per referral",
      icon: TrendingUp,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/30",
    },
  ]

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Referral Program</h1>
        <p className="text-gray-600 dark:text-gray-300">Invite friends and earn rewards together</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Total Referrals</h3>
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{referralStats.totalReferrals}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <UserPlus className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Active Referrals</h3>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{referralStats.activeReferrals}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Total Earnings</h3>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">${referralStats.totalEarnings.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">This Month</h3>
                <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">${referralStats.thisMonth.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Referral Link Section */}
      <Card>
        <CardHeader>
          <CardTitle>Your Referral Link</CardTitle>
          <CardDescription>Share this link with friends to start earning rewards</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">Referral Link</label>
              <div className="flex gap-2">
                <Input value={referralLink} readOnly className="flex-1" />
                <Button
                  onClick={() => copyToClipboard(referralLink)}
                  variant="outline"
                  className="min-w-[100px]"
                >
                  {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  {copied ? "Copied!" : "Copy"}
                </Button>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">Referral Code</label>
              <div className="flex gap-2">
                <Input value={referralCode} readOnly className="flex-1" />
                <Button
                  onClick={() => copyToClipboard(referralCode)}
                  variant="outline"
                  className="min-w-[100px]"
                >
                  {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  {copied ? "Copied!" : "Copy"}
                </Button>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Share2 className="w-4 h-4 mr-2" />
              Share on Social Media
            </Button>
            <Button variant="outline" className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
              <ExternalLink className="w-4 h-4 mr-2" />
              View Referral Terms
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Rewards Tiers */}
      <Card>
        <CardHeader>
          <CardTitle>Reward Tiers</CardTitle>
          <CardDescription>Earn more as you refer more friends</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {rewards.map((reward, index) => {
              const IconComponent = reward.icon
              return (
                <Card key={index} className="text-center">
                  <CardContent className="pt-6">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full ${reward.bgColor} flex items-center justify-center`}>
                      <IconComponent className={`w-8 h-8 ${reward.color}`} />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{reward.tier}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{reward.referrals} referrals</p>
                    <p className="font-semibold text-green-600 dark:text-green-400">{reward.reward}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Referrals */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Referrals</CardTitle>
          <CardDescription>People you've recently referred</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentReferrals.map((referral) => (
              <div
                key={referral.id}
                className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white">{referral.name}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{referral.email}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Joined: {referral.joinedDate}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900 dark:text-white">${referral.earnings.toFixed(2)}</p>
                  <Badge
                    className={
                      referral.status === "active"
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                        : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                    }
                  >
                    {referral.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* How It Works */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
        <CardContent className="pt-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">How It Works</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600 dark:text-blue-400">1</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Share Your Link</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">Send your referral link to friends and family</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-green-600 dark:text-green-400">2</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">They Sign Up</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">Your friends register using your referral link</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-purple-600 dark:text-purple-400">3</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Earn Rewards</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">Get paid when they complete their first challenge</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
