"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  DollarSign,
  TrendingUp,
  Award,
  Target,
  BarChart3,
  ArrowRight,
  CheckCircle,
  Clock,
  Star,
  Share2,
  ExternalLink,
} from "lucide-react"
import { useState } from "react"

export default function AffiliateProgram() {
  const [selectedPeriod, setSelectedPeriod] = useState("month")

  const affiliateStats = {
    totalEarnings: 8750.00,
    thisMonth: 1250.00,
    totalReferrals: 45,
    activeReferrals: 32,
    conversionRate: 78.5,
    averageCommission: 125.00,
  }

  const performanceMetrics = [
    {
      label: "Total Earnings",
      value: "$8,750",
      change: "+12.5%",
      positive: true,
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/30",
    },
    {
      label: "Active Referrals",
      value: "32",
      change: "+8.2%",
      positive: true,
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
    },
    {
      label: "Conversion Rate",
      value: "78.5%",
      change: "+2.1%",
      positive: true,
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
    },
    {
      label: "Avg Commission",
      value: "$125",
      change: "+5.3%",
      positive: true,
      icon: Award,
      color: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
    },
  ]

  const recentCommissions = [
    {
      id: "1",
      referral: "John Smith",
      challenge: "$50K Challenge",
      commission: 150.00,
      status: "paid",
      date: "2024-01-15",
    },
    {
      id: "2",
      referral: "Sarah Johnson",
      challenge: "$25K Challenge",
      commission: 75.00,
      status: "pending",
      date: "2024-01-14",
    },
    {
      id: "3",
      referral: "Mike Wilson",
      challenge: "$100K Challenge",
      commission: 200.00,
      status: "paid",
      date: "2024-01-12",
    },
  ]

  const commissionTiers = [
    {
      tier: "Bronze",
      referrals: "1-10",
      commission: "15%",
      color: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
      current: false,
    },
    {
      tier: "Silver",
      referrals: "11-25",
      commission: "20%",
      color: "text-gray-600",
      bgColor: "bg-gray-100 dark:bg-gray-900/30",
      current: true,
    },
    {
      tier: "Gold",
      referrals: "26-50",
      commission: "25%",
      color: "text-yellow-600",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/30",
      current: false,
    },
    {
      tier: "Platinum",
      referrals: "51+",
      commission: "30%",
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
      current: false,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Affiliate Program</h1>
        <p className="text-gray-600 dark:text-gray-300">Earn commissions by promoting FTMO challenges</p>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {performanceMetrics.map((metric, index) => {
          const IconComponent = metric.icon
          return (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`w-12 h-12 ${metric.bgColor} rounded-full flex items-center justify-center`}>
                    <IconComponent className={`w-6 h-6 ${metric.color}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 dark:text-white">{metric.label}</h3>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{metric.value}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium ${metric.positive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {metric.change}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">vs last month</span>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Commission Tiers */}
      <Card>
        <CardHeader>
          <CardTitle>Commission Tiers</CardTitle>
          <CardDescription>Earn higher commissions as you refer more traders</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {commissionTiers.map((tier, index) => {
              const IconComponent = Award
              return (
                <Card key={index} className={`relative ${tier.current ? 'ring-2 ring-blue-500' : ''}`}>
                  <CardContent className="pt-6 text-center">
                    {tier.current && (
                      <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white">
                        Current
                      </Badge>
                    )}
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full ${tier.bgColor} flex items-center justify-center`}>
                      <IconComponent className={`w-8 h-8 ${tier.color}`} />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{tier.tier}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{tier.referrals} referrals</p>
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">{tier.commission}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Commission Rate</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Progress to Next Tier */}
      <Card>
        <CardHeader>
          <CardTitle>Progress to Gold Tier</CardTitle>
          <CardDescription>You need 14 more referrals to reach Gold tier (25% commission)</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-300">Current: 32 referrals</span>
              <span className="text-gray-600 dark:text-gray-300">Target: 50 referrals</span>
            </div>
            <Progress value={64} className="h-3" />
            <div className="text-right text-sm text-gray-500 dark:text-gray-400">
              64% Complete
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Commissions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Commissions</CardTitle>
          <CardDescription>Your latest commission earnings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentCommissions.map((commission) => (
              <div
                key={commission.id}
                className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <DollarSign className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white">{commission.referral}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{commission.challenge}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{commission.date}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900 dark:text-white">${commission.commission.toFixed(2)}</p>
                  <Badge className={getStatusColor(commission.status)}>
                    {commission.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Marketing Tools */}
      <Card>
        <CardHeader>
          <CardTitle>Marketing Tools</CardTitle>
          <CardDescription>Resources to help you promote FTMO</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardContent className="pt-6 text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Share2 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Banners & Graphics</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">Professional marketing materials</p>
                <Button variant="outline" size="sm" className="w-full">
                  Download
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardContent className="pt-6 text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Analytics Dashboard</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">Track your performance</p>
                <Button variant="outline" size="sm" className="w-full">
                  View
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardContent className="pt-6 text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ExternalLink className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Affiliate Portal</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">Advanced tracking tools</p>
                <Button variant="outline" size="sm" className="w-full">
                  Access
                </Button>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
        <CardContent className="pt-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Start Earning?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Join our affiliate program and start earning commissions by promoting FTMO challenges to your network.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                <Share2 className="w-4 h-4 mr-2" />
                Get Started
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              <Button variant="outline" className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 px-8 py-3">
                <ExternalLink className="w-4 h-4 mr-2" />
                Learn More
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
