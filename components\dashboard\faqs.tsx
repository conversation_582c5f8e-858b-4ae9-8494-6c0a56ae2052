"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Search, MessageCircle, ArrowRight, BookOpen, Video, FileText } from "lucide-react"
import { useState } from "react"

export default function FAQs() {
  const [searchQuery, setSearchQuery] = useState("")

  const faqCategories = [
    {
      title: "Getting Started",
      icon: BookOpen,
      color: "green",
      faqs: [
        {
          question: "How do I start a trading challenge?",
          answer:
            "To start a trading challenge, navigate to the 'New Challenge' section, select your preferred account size and currency, then complete the purchase. You'll receive your trading credentials within 24 hours.",
        },
        {
          question: "What are the minimum requirements to trade?",
          answer:
            "You must be at least 18 years old, have a valid government-issued ID, and complete our KYC verification process. No prior trading experience is required, but we recommend completing our educational materials first.",
        },
        {
          question: "How long does account verification take?",
          answer:
            "Account verification typically takes 24-48 hours. During peak times, it may take up to 72 hours. You'll receive an email notification once your account is verified and ready for trading.",
        },
      ],
    },
    {
      title: "Trading Rules",
      icon: FileText,
      color: "blue",
      faqs: [
        {
          question: "What are the daily drawdown limits?",
          answer:
            "The maximum daily drawdown is 5% of your account balance. This is calculated from your account's highest point (high-water mark) during the trading day. The limit resets at 5 PM EST each day.",
        },
        {
          question: "Can I hold positions over the weekend?",
          answer:
            "Yes, you can hold positions over the weekend. However, be aware of potential gaps and increased volatility when markets reopen. Ensure your positions don't violate risk management rules during weekend gaps.",
        },
        {
          question: "Are there any prohibited trading strategies?",
          answer:
            "We allow most trading strategies including scalping, swing trading, and algorithmic trading. However, we prohibit account manipulation, coordinated trading between multiple accounts, and exploiting platform errors.",
        },
      ],
    },
    {
      title: "Payouts & Withdrawals",
      icon: Video,
      color: "purple",
      faqs: [
        {
          question: "How often can I request withdrawals?",
          answer:
            "Once you're a funded trader, you can request withdrawals weekly after completing the minimum 4 trading days requirement. Payouts are processed within 24-48 hours of approval.",
        },
        {
          question: "What payment methods are available?",
          answer:
            "We support bank transfers (free), PayPal (2.5% fee), and cryptocurrency transfers (1% fee). Bank transfers take 1-3 business days, while PayPal and crypto are processed instantly.",
        },
        {
          question: "Is there a minimum withdrawal amount?",
          answer:
            "The minimum withdrawal amount varies by payment method: $50 for bank transfers, $25 for PayPal, and $100 for cryptocurrency. There's no maximum limit on withdrawals.",
        },
      ],
    },
  ]

  const quickLinks = [
    {
      title: "Trading Platform Guide",
      description: "Learn how to use MetaTrader 4 and 5",
      icon: Video,
      color: "blue",
    },
    {
      title: "Risk Management",
      description: "Best practices for managing your account",
      icon: FileText,
      color: "green",
    },
    {
      title: "Challenge Rules",
      description: "Complete guide to our evaluation process",
      icon: BookOpen,
      color: "purple",
    },
  ]

  const filteredFAQs = faqCategories
    .map((category) => ({
      ...category,
      faqs: category.faqs.filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    }))
    .filter((category) => category.faqs.length > 0)

  const getColorClasses = (color: string) => {
    const colorMap = {
      green: {
        bg: "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20",
        border: "border-green-200 dark:border-green-400/20",
        icon: "text-green-600 dark:text-green-400",
        iconBg: "from-green-500 to-emerald-500",
      },
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.green
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
        <div className="relative z-10">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Frequently Asked Questions</h1>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-6">
            Find answers to common questions about trading, challenges, and payouts
          </p>

          {/* Search */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 pl-10"
            />
          </div>
        </div>
      </div>

      {/* Quick Links */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {quickLinks.map((link, index) => {
          const colors = getColorClasses(link.color)
          const IconComponent = link.icon

          return (
            <div
              key={index}
              className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group cursor-pointer`}
            >
              <div className="flex items-center gap-4 mb-4">
                <div
                  className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">{link.title}</h3>
                </div>
              </div>
              <p className="text-gray-700 dark:text-white/70 mb-4">{link.description}</p>
              <Button
                variant="ghost"
                size="sm"
                className={`${colors.icon} hover:bg-white/20 dark:hover:bg-white/10 p-0`}
              >
                Learn More
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )
        })}
      </div>

      {/* FAQ Categories */}
      <div className="space-y-8">
        {(searchQuery ? filteredFAQs : faqCategories).map((category, categoryIndex) => {
          const colors = getColorClasses(category.color)
          const IconComponent = category.icon

          return (
            <div
              key={categoryIndex}
              className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl"
            >
              <div className="flex items-center gap-4 mb-6">
                <div
                  className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center shadow-lg`}
                >
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{category.title}</h2>
                <Badge className={`${colors.icon} bg-transparent border ${colors.border}`}>
                  {category.faqs.length} questions
                </Badge>
              </div>

              <Accordion type="single" collapsible className="space-y-4">
                {category.faqs.map((faq, faqIndex) => (
                  <AccordionItem
                    key={faqIndex}
                    value={`${categoryIndex}-${faqIndex}`}
                    className="border border-gray-200 dark:border-white/10 rounded-2xl px-6 py-2 bg-white/50 dark:bg-white/5 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-white/10 transition-all duration-300"
                  >
                    <AccordionTrigger className="text-left hover:no-underline group">
                      <span className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                        {faq.question}
                      </span>
                    </AccordionTrigger>
                    <AccordionContent className="pt-4 pb-2">
                      <p className="text-base text-gray-700 dark:text-white/70 leading-relaxed">{faq.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          )
        })}
      </div>

      {/* Contact Support */}
      <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-3xl p-8 shadow-xl">
        <div className="text-center">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mx-auto mb-6 shadow-lg">
            <MessageCircle className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Still have questions?</h3>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-8 max-w-2xl mx-auto">
            Our support team is available 24/7 to help you with any questions about trading, challenges, or account
            management.
          </p>
          <Button className="rounded-full bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 text-white hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
            Contact Support
            <MessageCircle className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  )
}
