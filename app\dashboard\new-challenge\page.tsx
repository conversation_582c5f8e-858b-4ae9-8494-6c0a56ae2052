"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { 
  DollarSign, 
  TrendingUp, 
  Zap, 
  Shield, 
  CheckCircle,
  Clock,
  Star,
  Gift,
  CreditCard,
  ArrowRight,
  Crown,
  Target,
  Rocket,
  Award,
  Users,
  Calendar,
  AlertCircle,
  Plus,
  Sparkles,
  TrendingDown,
  BarChart3
} from "lucide-react"
import Image from "next/image"

export default function NewChallengePage() {
  const [selectedAccountSize, setSelectedAccountSize] = useState("")
  const [selectedPlatform, setSelectedPlatform] = useState("")
  const [selectedAccountType, setSelectedAccountType] = useState("")
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("")

  const accountSizes = [
    { id: "5k", name: "$5,000", price: 99, popular: false, features: ["Basic support", "Standard platform"] },
    { id: "10k", name: "$10,000", price: 199, popular: false, features: ["Priority support", "Advanced tools"] },
    { id: "25k", name: "$25,000", price: 399, popular: false, features: ["Premium support", "Full access"] },
    { id: "50k", name: "$50,000", price: 699, popular: true, giveaway: true, features: ["VIP support", "Exclusive features", "Giveaway entry"] },
    { id: "100k", name: "$100,000", price: 1299, popular: false, features: ["Elite support", "Custom solutions"] },
    { id: "200k", name: "$200,000", price: 2499, popular: false, features: ["Dedicated manager", "Premium features"] },
  ]

  const platforms = [
    { 
      id: "mt4", 
      name: "MetaTrader 4", 
      icon: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png",
      description: "Classic trading platform with proven reliability",
      features: ["User-friendly interface", "Extensive indicators", "EA support"]
    },
    { 
      id: "mt5", 
      name: "MetaTrader 5", 
      icon: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/MT5-removebg-preview_1_lqagz7.png",
      description: "Advanced trading platform with enhanced features",
      features: ["Advanced charting", "More timeframes", "Better backtesting"]
    },
  ]

  const accountTypes = [
    { 
      id: "hft", 
      name: "HFT (High-Frequency Trading)", 
      description: "Ultra-fast execution, scalping allowed",
      features: ["Scalping allowed", "News trading", "EA trading", "No time limits"],
      icon: Zap,
      color: "text-purple-600",
      bgColor: "bg-purple-50 dark:bg-purple-900/20",
      borderColor: "border-purple-200 dark:border-purple-700"
    },
    { 
      id: "instant", 
      name: "INSTANT", 
      description: "Immediate account activation",
      features: ["Instant activation", "No evaluation", "Direct trading", "Full profit share"],
      icon: Rocket,
      color: "text-blue-600",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
      borderColor: "border-blue-200 dark:border-blue-700"
    },
    { 
      id: "phase1", 
      name: "PHASE 1", 
      description: "First evaluation phase",
      features: ["8% profit target", "5% maximum drawdown", "30 days time limit", "No news trading"],
      icon: Target,
      color: "text-green-600",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      borderColor: "border-green-200 dark:border-green-700"
    },
    { 
      id: "phase2", 
      name: "PHASE 2", 
      description: "Second evaluation phase",
      features: ["5% profit target", "5% maximum drawdown", "60 days time limit", "No news trading"],
      icon: Award,
      color: "text-orange-600",
      bgColor: "bg-orange-50 dark:bg-orange-900/20",
      borderColor: "border-orange-200 dark:border-orange-700"
    },
  ]

  const paymentMethods = [
    { id: "card", name: "Credit/Debit Card", icon: CreditCard, description: "Secure payment processing" },
    { id: "crypto", name: "Cryptocurrency", icon: Zap, description: "Fast and anonymous payments" },
    { id: "bank", name: "Bank Transfer", icon: Shield, description: "Traditional bank transfer" },
  ]

  const selectedSize = accountSizes.find(size => size.id === selectedAccountSize)
  const selectedPlatformData = platforms.find(platform => platform.id === selectedPlatform)
  const selectedTypeData = accountTypes.find(type => type.id === selectedAccountType)

  const calculateTotal = () => {
    if (!selectedSize) return 0
    return selectedSize.price
  }

  const isFormComplete = selectedAccountSize && selectedPlatform && selectedAccountType

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Plus className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">New Trading Challenge</h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Choose your account size, platform, and challenge type to start your trading journey
          </p>
        </div>
        <Button variant="outline" className="border-2 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800">
          <Gift className="w-4 h-4 mr-2" />
          View Giveaways
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500 rounded-lg">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Active Challenges</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">12</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500 rounded-lg">
                <Award className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">8</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500 rounded-lg">
                <DollarSign className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Invested</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">$2,499</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-orange-200 dark:border-orange-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-500 rounded-lg">
                <BarChart3 className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Success Rate</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">67%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Selection Panel */}
        <div className="lg:col-span-2 space-y-6">
          {/* Account Size Selection */}
          <Card className="border-2 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                Select Account Size
              </CardTitle>
              <CardDescription>
                Choose the account size that matches your trading goals and risk tolerance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {accountSizes.map((size) => (
                  <div
                    key={size.id}
                    className={`relative cursor-pointer rounded-xl border-2 p-4 transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                      selectedAccountSize === size.id
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg scale-105"
                        : "border-gray-200 dark:border-gray-700 hover:border-blue-300"
                    }`}
                    onClick={() => setSelectedAccountSize(size.id)}
                  >
                    {size.popular && (
                      <Badge className="absolute -top-2 -right-2 bg-blue-600 text-white shadow-lg">
                        <Sparkles className="w-3 h-3 mr-1" />
                        Popular
                      </Badge>
                    )}
                    {size.giveaway && (
                      <Badge className="absolute -top-2 -left-2 bg-green-600 text-white shadow-lg">
                        <Gift className="w-3 h-3 mr-1" />
                        Giveaway
                      </Badge>
                    )}
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                        {size.name}
                      </div>
                      <div className="text-lg font-semibold text-green-600 mb-2">
                        ${size.price}
                      </div>
                      <div className="text-xs text-gray-500 mb-3">One-time payment</div>
                      <div className="space-y-1">
                        {size.features.map((feature, index) => (
                          <div key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-center justify-center gap-1">
                            <CheckCircle className="w-3 h-3 text-green-500" />
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Platform Selection */}
          <Card className="border-2 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                Select Trading Platform
              </CardTitle>
              <CardDescription>
                Choose your preferred trading platform based on your experience level
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup value={selectedPlatform} onValueChange={setSelectedPlatform}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {platforms.map((platform) => (
                    <div
                      key={platform.id}
                      className={`relative cursor-pointer rounded-xl border-2 p-6 transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                        selectedPlatform === platform.id
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg scale-105"
                          : "border-gray-200 dark:border-gray-700 hover:border-blue-300"
                      }`}
                      onClick={() => setSelectedPlatform(platform.id)}
                    >
                      <RadioGroupItem value={platform.id} className="sr-only" />
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          <Image
                            src={platform.icon}
                            alt={platform.name}
                            width={48}
                            height={48}
                            className="w-12 h-12 object-contain"
                          />
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-gray-900 dark:text-white mb-1">
                            {platform.name}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            {platform.description}
                          </div>
                          <div className="space-y-1">
                            {platform.features.map((feature, index) => (
                              <div key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1">
                                <CheckCircle className="w-3 h-3 text-green-500" />
                                {feature}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          {/* Account Type Selection */}
          <Card className="border-2 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-orange-600" />
                Select Account Type
              </CardTitle>
              <CardDescription>
                Choose your preferred challenge type based on your trading strategy
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {accountTypes.map((type) => {
                  const IconComponent = type.icon
                  return (
                    <div
                      key={type.id}
                      className={`relative cursor-pointer rounded-xl border-2 p-6 transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                        selectedAccountType === type.id
                          ? `${type.borderColor} ${type.bgColor} shadow-lg scale-105`
                          : "border-gray-200 dark:border-gray-700 hover:border-blue-300"
                      }`}
                      onClick={() => setSelectedAccountType(type.id)}
                    >
                      <div className="flex items-start gap-4">
                        <div className={`p-3 rounded-lg ${type.bgColor} ${type.color}`}>
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {type.name}
                            </h3>
                            <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                              Available
                            </Badge>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-3">
                            {type.description}
                          </p>
                          <div className="grid grid-cols-2 gap-2">
                            {type.features.map((feature, index) => (
                              <div key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                                <CheckCircle className="w-4 h-4 text-green-500" />
                                {feature}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card className="sticky top-8 border-2 border-gray-200 dark:border-gray-700">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-blue-600" />
                Order Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Selected Options */}
              {selectedSize && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">
                        {selectedSize.name} Account
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {selectedPlatformData?.name} • {selectedTypeData?.name}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600 text-lg">${selectedSize.price}</div>
                      {selectedSize.giveaway && (
                        <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 text-xs mt-1">
                          <Gift className="w-3 h-3 mr-1" />
                          Giveaway Eligible
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Features */}
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  What's Included:
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    Professional trading platform
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    Real-time market data
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    24/7 customer support
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    Educational resources
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    Community access
                  </div>
                </div>
              </div>

              <Separator />

              {/* Total */}
              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg border border-green-200 dark:border-green-700">
                <span className="text-lg font-semibold text-gray-900 dark:text-white">Total</span>
                <span className="text-2xl font-bold text-green-600">${calculateTotal()}</span>
              </div>

              {/* Payment Methods */}
              {isFormComplete && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white">Payment Method:</h4>
                  <RadioGroup value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                    <div className="space-y-3">
                      {paymentMethods.map((method) => {
                        const IconComponent = method.icon
                        return (
                          <div
                            key={method.id}
                            className={`flex items-center gap-3 p-3 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:scale-105 ${
                              selectedPaymentMethod === method.id
                                ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg scale-105"
                                : "border-gray-200 dark:border-gray-700 hover:border-blue-300"
                            }`}
                            onClick={() => setSelectedPaymentMethod(method.id)}
                          >
                            <RadioGroupItem value={method.id} className="sr-only" />
                            <IconComponent className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                            <div className="flex-1">
                              <span className="font-medium text-gray-900 dark:text-white">
                                {method.name}
                              </span>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {method.description}
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </RadioGroup>
                </div>
              )}

              {/* Action Button */}
              <Button 
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                disabled={!isFormComplete || !selectedPaymentMethod}
              >
                {selectedPaymentMethod ? (
                  <>
                    <CreditCard className="w-5 h-5 mr-2" />
                    Proceed to Payment
                  </>
                ) : (
                  <>
                    <ArrowRight className="w-5 h-5 mr-2" />
                    Complete Selection
                  </>
                )}
              </Button>

              {/* Giveaway Notice */}
              {selectedSize?.giveaway && (
                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-green-700 dark:text-green-400 mb-2">
                    <Gift className="w-4 h-4" />
                    <span className="text-sm font-medium">Giveaway Eligible!</span>
                  </div>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    This account size qualifies for our monthly giveaway. Complete the challenge to enter!
                  </p>
                </div>
              )}

              {/* Security Notice */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                <div className="flex items-center gap-2 text-blue-700 dark:text-blue-400 mb-2">
                  <Shield className="w-4 h-4" />
                  <span className="text-sm font-medium">Secure Payment</span>
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  All payments are processed securely with bank-level encryption.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 