"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardHeader } from "@/components/ui/card"
import { Check, X, Star, Trophy, Zap, DollarSign, Clock, Shield, Users, TrendingUp, Globe } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"

export default function CompareSection() {
  const { t } = useLanguage()

  const keyFeatures = [
    {
      title: "Up to $400K Funding",
      description: "Start with $10K and scale up to $400K in funding",
      icon: DollarSign,
      color: "from-blue-500 to-cyan-500",
      value: "$400K",
      highlight: true,
    },
    {
      title: "90% Profit Split",
      description: "Keep up to 90% of your trading profits",
      icon: TrendingUp,
      color: "from-green-500 to-emerald-500",
      value: "90%",
      highlight: true,
    },
    {
      title: "1-Step Evaluation",
      description: "Simple evaluation process to get funded quickly",
      icon: Zap,
      color: "from-purple-500 to-pink-500",
      value: "1-Step",
      highlight: true,
    },
    {
      title: "No Time Limits",
      description: "Take as long as you need to complete evaluation",
      icon: Clock,
      color: "from-orange-500 to-amber-500",
      value: "Unlimited",
      highlight: true,
    },
    {
      title: "24/7 Expert Support",
      description: "Round-the-clock support from professional traders",
      icon: Users,
      color: "from-indigo-500 to-blue-500",
      value: "24/7",
      highlight: true,
    },
    {
      title: "Advanced Security",
      description: "Bank-level encryption and secure data protection",
      icon: Shield,
      color: "from-teal-500 to-cyan-500",
      value: "Bank-Level",
      highlight: true,
    },
  ]

  const competitors = [
    {
      name: "Apex Capital",
      logo: "AC",
      isUs: true,
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-500/10 to-cyan-500/10",
      borderColor: "border-blue-200 dark:border-blue-400/20",
      features: {
        profitSplit: "90%",
        maxFunding: "$400K",
        evaluation: "1-Step",
        timeLimit: "No Limit",
        weekendHolding: true,
        expertAdvisor: true,
        newsTrading: true,
        scaling: true,
        support: "24/7",
        refund: "100%",
      },
    },
    {
      name: "Competitor A",
      logo: "CA",
      isUs: false,
      color: "from-gray-400 to-gray-500",
      bgColor: "from-gray-400/5 to-gray-500/5",
      borderColor: "border-gray-200 dark:border-gray-400/20",
      features: {
        profitSplit: "70%",
        maxFunding: "$200K",
        evaluation: "2-Step",
        timeLimit: "30 Days",
        weekendHolding: false,
        expertAdvisor: false,
        newsTrading: false,
        scaling: false,
        support: "Business Hours",
        refund: "50%",
      },
    },
    {
      name: "Competitor B",
      logo: "CB",
      isUs: false,
      color: "from-gray-400 to-gray-500",
      bgColor: "from-gray-400/5 to-gray-500/5",
      borderColor: "border-gray-200 dark:border-gray-400/20",
      features: {
        profitSplit: "75%",
        maxFunding: "$300K",
        evaluation: "2-Step",
        timeLimit: "45 Days",
        weekendHolding: true,
        expertAdvisor: true,
        newsTrading: false,
        scaling: false,
        support: "Email Only",
        refund: "0%",
      },
    },
    {
      name: "Competitor C",
      logo: "CC",
      isUs: false,
      color: "from-gray-400 to-gray-500",
      bgColor: "from-gray-400/5 to-gray-500/5",
      borderColor: "border-gray-200 dark:border-gray-400/20",
      features: {
        profitSplit: "60%",
        maxFunding: "$150K",
        evaluation: "3-Step",
        timeLimit: "60 Days",
        weekendHolding: false,
        expertAdvisor: false,
        newsTrading: false,
        scaling: false,
        support: "Chat Only",
        refund: "25%",
      },
    },
  ]

  const comparisonFeatures = [
    { key: "profitSplit", label: "Profit Split", icon: Trophy },
    { key: "maxFunding", label: "Max Funding", icon: Star },
    { key: "evaluation", label: "Evaluation", icon: Zap },
    { key: "timeLimit", label: "Time Limit", icon: null },
    { key: "weekendHolding", label: "Weekend Holding", icon: null },
    { key: "expertAdvisor", label: "Expert Advisor", icon: null },
    { key: "newsTrading", label: "News Trading", icon: null },
    { key: "scaling", label: "Account Scaling", icon: null },
    { key: "support", label: "Support", icon: null },
    { key: "refund", label: "Refund Policy", icon: null },
  ]

  return (
    <section className="py-24 md:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-50/20 to-transparent dark:via-blue-950/10" />
      <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 dark:from-indigo-500/10 dark:to-purple-500/10 rounded-full blur-3xl" />

      <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16 relative">
        {/* Section Header */}
        <div className="text-center mb-20 md:mb-28">
          <Badge
            variant="outline"
            className="mb-6 text-sm font-light border-blue-200 dark:border-blue-400/20 text-blue-600 dark:text-blue-400 px-4 py-2"
          >
            <Trophy className="w-4 h-4 mr-2" />
            {t("compare.badge")}
          </Badge>

          <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="text-gray-900 dark:text-white">Why Choose</span>{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              Apex Capital
            </span>
          </h2>

          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 max-w-3xl mx-auto leading-relaxed">
            See how we stack up against the competition. Better terms, higher funding, and superior support.
          </p>
        </div>

        {/* Side-by-Side Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Left Side - Key Features */}
          <div className="space-y-8">
            <div className="text-center lg:text-left">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Key Features
              </h3>
              <p className="text-lg text-gray-700 dark:text-white/70">
                Discover what makes Apex Capital the preferred choice for traders worldwide
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {keyFeatures.map((feature, index) => {
                const IconComponent = feature.icon
                return (
                  <div
                    key={index}
                    className="group bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] hover:-translate-y-1"
                  >
                    <div className="flex items-center gap-4 mb-4">
                      <div
                        className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                      >
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                          {feature.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-white/60">{feature.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
                        {feature.value}
                      </span>
                      {feature.highlight && (
                        <Badge className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-400/20">
                          <Check className="w-3 h-3 mr-1" />
                          Best
                        </Badge>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Right Side - Comparison Table */}
          <div className="space-y-8">
            <div className="text-center lg:text-left">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Compare & Choose
              </h3>
              <p className="text-lg text-gray-700 dark:text-white/70">
                See how we compare to other prop firms in the market
              </p>
            </div>

            <div className="overflow-x-auto">
              <div className="min-w-[600px]">
                {/* Header Row */}
                <div className="grid grid-cols-4 gap-4 mb-6">
                  <div className="text-left">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Features</h4>
                  </div>
                  {competitors.map((competitor, index) => (
                    <Card
                      key={index}
                      className={`${
                        competitor.isUs
                          ? `bg-gradient-to-br ${competitor.bgColor} dark:${competitor.bgColor.replace("/10", "/15")} border ${competitor.borderColor} ring-2 ring-blue-500/20 dark:ring-blue-400/20`
                          : "bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700"
                      } relative overflow-hidden`}
                    >
                      <CardHeader className="text-center pb-3">
                        {competitor.isUs && (
                          <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-0 text-xs">
                            Best
                          </Badge>
                        )}
                        <div
                          className={`w-8 h-8 mx-auto rounded-lg bg-gradient-to-r ${competitor.color} flex items-center justify-center mb-2 shadow-lg`}
                        >
                          <span className="text-white font-bold text-sm">{competitor.logo}</span>
                        </div>
                        <h5
                          className={`font-bold text-sm ${competitor.isUs ? "text-blue-600 dark:text-blue-400" : "text-gray-700 dark:text-white/80"}`}
                        >
                          {competitor.name}
                        </h5>
                      </CardHeader>
                    </Card>
                  ))}
                </div>

                {/* Feature Rows */}
                <div className="space-y-3">
                  {comparisonFeatures.map((feature, featureIndex) => {
                    const IconComponent = feature.icon
                    return (
                      <div key={featureIndex} className="grid grid-cols-4 gap-4 items-center">
                        <div className="flex items-center gap-2">
                          {IconComponent && <IconComponent className="w-4 h-4 text-gray-600 dark:text-white/60" />}
                          <span className="text-sm font-medium text-gray-900 dark:text-white">{feature.label}</span>
                        </div>
                        {competitors.map((competitor, competitorIndex) => (
                          <div
                            key={competitorIndex}
                            className={`text-center p-3 rounded-lg ${
                              competitor.isUs
                                ? "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-400/20"
                                : "bg-gray-50 dark:bg-gray-800/30"
                            }`}
                          >
                            {typeof competitor.features[feature.key as keyof typeof competitor.features] === "boolean" ? (
                              competitor.features[feature.key as keyof typeof competitor.features] ? (
                                <Check className="w-4 h-4 text-green-500 mx-auto" />
                              ) : (
                                <X className="w-4 h-4 text-red-500 mx-auto" />
                              )
                            ) : (
                              <span
                                className={`text-sm font-semibold ${
                                  competitor.isUs ? "text-blue-600 dark:text-blue-400" : "text-gray-700 dark:text-white/80"
                                }`}
                              >
                                {competitor.features[feature.key as keyof typeof competitor.features]}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="mt-16 md:mt-20 text-center">
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 rounded-3xl p-8 md:p-12 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Experience the Difference?
            </h3>
            <p className="text-lg text-gray-700 dark:text-white/70 mb-8 max-w-2xl mx-auto">
              Join thousands of traders who chose Apex Capital for better terms, higher funding, and superior support.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Badge
                variant="outline"
                className="border-blue-200 dark:border-blue-400/20 text-blue-600 dark:text-blue-400 px-4 py-2"
              >
                <Star className="w-4 h-4 mr-2" />
                4.9/5 Rating
              </Badge>
              <Badge
                variant="outline"
                className="border-cyan-200 dark:border-cyan-400/20 text-cyan-600 dark:text-cyan-400 px-4 py-2"
              >
                <Trophy className="w-4 h-4 mr-2" />
                15K+ Funded Traders
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
