"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Star,
  Trophy,
  Gift,
  Edit,
  Camera,
  Shield,
  Settings,
  Crown,
  Zap,
  Award,
  TrendingUp
} from "lucide-react"
import { useState } from "react"

export default function Profile() {
  const [isEditing, setIsEditing] = useState(false)
  const [userInfo, setUserInfo] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    location: "New York, USA",
    joinDate: "January 2024",
    membershipLevel: "Premium"
  })

  const userStats = {
    totalPoints: 2450,
    pointsThisMonth: 320,
    referralPoints: 850,
    bonusPoints: 1600,
    level: "Gold",
    nextLevelPoints: 550,
    completedChallenges: 8,
    successRate: 87.5
  }

  const achievements = [
    {
      title: "First Challenge",
      description: "Complete your first trading challenge",
      icon: Trophy,
      earned: true,
      points: 100,
      date: "Jan 15, 2024"
    },
    {
      title: "Referral Master",
      description: "Refer 10 successful traders",
      icon: Crown,
      earned: true,
      points: 500,
      date: "Feb 20, 2024"
    },
    {
      title: "Profit Streak",
      description: "Achieve 5 consecutive profitable trades",
      icon: TrendingUp,
      earned: true,
      points: 250,
      date: "Mar 10, 2024"
    },
    {
      title: "Elite Trader",
      description: "Reach 90% success rate",
      icon: Star,
      earned: false,
      points: 1000,
      date: null
    }
  ]

  const pointsHistory = [
    {
      type: "Referral Bonus",
      points: 150,
      date: "Mar 15, 2024",
      description: "New user registration via your link"
    },
    {
      type: "Challenge Completion",
      points: 200,
      date: "Mar 12, 2024",
      description: "Successfully completed $50K challenge"
    },
    {
      type: "Achievement Unlock",
      points: 250,
      date: "Mar 10, 2024",
      description: "Profit Streak achievement earned"
    },
    {
      type: "Monthly Bonus",
      points: 100,
      date: "Mar 01, 2024",
      description: "Active trader monthly bonus"
    }
  ]

  const membershipBenefits = [
    "Priority customer support",
    "Exclusive trading signals",
    "Advanced analytics dashboard",
    "Higher referral commissions",
    "Early access to new features"
  ]

  return (
    <div className="space-y-8">
      {/* Profile Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl">
        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
          <div className="relative">
            <div className="w-24 h-24 rounded-3xl bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold text-2xl shadow-xl">
              {userInfo.name.split(' ').map(n => n[0]).join('')}
            </div>
            <Button size="sm" className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 shadow-lg">
              <Camera className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{userInfo.name}</h1>
              <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                <Crown className="w-4 h-4 mr-1" />
                {userInfo.membershipLevel}
              </Badge>
            </div>
            <p className="text-gray-600 dark:text-white/70 mb-4">Member since {userInfo.joinDate}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
                <div className="flex items-center gap-2 mb-2">
                  <Star className="w-5 h-5 text-yellow-500" />
                  <span className="font-semibold text-gray-900 dark:text-white">Total Points</span>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{userStats.totalPoints.toLocaleString()}</div>
              </div>
              
              <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
                <div className="flex items-center gap-2 mb-2">
                  <Trophy className="w-5 h-5 text-blue-500" />
                  <span className="font-semibold text-gray-900 dark:text-white">Success Rate</span>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{userStats.successRate}%</div>
              </div>
              
              <div className="bg-white/50 dark:bg-white/5 rounded-2xl p-4 border border-gray-200 dark:border-white/10">
                <div className="flex items-center gap-2 mb-2">
                  <Award className="w-5 h-5 text-purple-500" />
                  <span className="font-semibold text-gray-900 dark:text-white">Level</span>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{userStats.level}</div>
              </div>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => setIsEditing(!isEditing)}
              className="bg-white/50 dark:bg-white/5 border-gray-200 dark:border-white/10"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Personal Information */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Personal Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-white/70 mb-2">
                  <User className="w-4 h-4 inline mr-2" />
                  Full Name
                </label>
                <Input 
                  value={userInfo.name} 
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-white/5"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-white/70 mb-2">
                  <Mail className="w-4 h-4 inline mr-2" />
                  Email Address
                </label>
                <Input 
                  value={userInfo.email} 
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-white/5"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-white/70 mb-2">
                  <Phone className="w-4 h-4 inline mr-2" />
                  Phone Number
                </label>
                <Input 
                  value={userInfo.phone} 
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-white/5"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-white/70 mb-2">
                  <MapPin className="w-4 h-4 inline mr-2" />
                  Location
                </label>
                <Input 
                  value={userInfo.location} 
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-white/5"
                />
              </div>
            </div>
            
            {isEditing && (
              <div className="flex gap-2 mt-6">
                <Button className="bg-green-600 hover:bg-green-700 text-white">
                  Save Changes
                </Button>
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
              </div>
            )}
          </div>

          {/* Points History */}
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Recent Points Activity</h3>

            <div className="space-y-4">
              {pointsHistory.map((item, index) => (
                <div key={index} className="flex items-center gap-4 p-4 rounded-xl bg-gray-50 dark:bg-white/5 hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200">
                  <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center shadow-lg">
                    <Gift className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-gray-900 dark:text-white">{item.type}</div>
                    <div className="text-sm text-gray-600 dark:text-white/60">{item.description}</div>
                    <div className="text-xs text-gray-500 dark:text-white/50">{item.date}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">+{item.points}</div>
                    <div className="text-xs text-gray-500 dark:text-white/50">points</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Points Breakdown */}
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Points Breakdown</h3>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 rounded-xl bg-blue-50 dark:bg-blue-500/10">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-blue-500 flex items-center justify-center">
                    <Gift className="w-4 h-4 text-white" />
                  </div>
                  <span className="font-medium text-gray-900 dark:text-white">Referral Points</span>
                </div>
                <span className="font-bold text-blue-600">{userStats.referralPoints}</span>
              </div>

              <div className="flex items-center justify-between p-3 rounded-xl bg-purple-50 dark:bg-purple-500/10">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-purple-500 flex items-center justify-center">
                    <Zap className="w-4 h-4 text-white" />
                  </div>
                  <span className="font-medium text-gray-900 dark:text-white">Bonus Points</span>
                </div>
                <span className="font-bold text-purple-600">{userStats.bonusPoints}</span>
              </div>

              <div className="flex items-center justify-between p-3 rounded-xl bg-green-50 dark:bg-green-500/10">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-green-500 flex items-center justify-center">
                    <Star className="w-4 h-4 text-white" />
                  </div>
                  <span className="font-medium text-gray-900 dark:text-white">This Month</span>
                </div>
                <span className="font-bold text-green-600">+{userStats.pointsThisMonth}</span>
              </div>
            </div>

            {/* Progress to Next Level */}
            <div className="mt-6 p-4 rounded-xl bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-500/10 dark:to-orange-500/10 border border-yellow-200 dark:border-yellow-400/20">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900 dark:text-white">Next Level Progress</span>
                <span className="text-sm text-gray-600 dark:text-white/60">{userStats.nextLevelPoints} points to go</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${((userStats.totalPoints % 1000) / 1000) * 100}%` }}
                />
              </div>
            </div>
          </div>

          {/* Achievements */}
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Achievements</h3>

            <div className="space-y-3">
              {achievements.map((achievement, index) => {
                const Icon = achievement.icon
                return (
                  <div key={index} className={`p-3 rounded-xl border transition-all duration-200 ${
                    achievement.earned
                      ? 'bg-green-50 dark:bg-green-500/10 border-green-200 dark:border-green-400/20'
                      : 'bg-gray-50 dark:bg-white/5 border-gray-200 dark:border-white/10 opacity-60'
                  }`}>
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                        achievement.earned
                          ? 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white'
                          : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                      }`}>
                        <Icon className="w-5 h-5" />
                      </div>
                      <div className="flex-1">
                        <div className="font-semibold text-gray-900 dark:text-white">{achievement.title}</div>
                        <div className="text-xs text-gray-600 dark:text-white/60">{achievement.description}</div>
                        {achievement.earned && achievement.date && (
                          <div className="text-xs text-green-600 dark:text-green-400">Earned {achievement.date}</div>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-yellow-600">+{achievement.points}</div>
                        <div className="text-xs text-gray-500 dark:text-white/50">points</div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Membership Benefits */}
          <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 dark:from-yellow-500/20 dark:to-orange-500/20 backdrop-blur-sm border border-yellow-200 dark:border-yellow-400/20 rounded-3xl p-6 shadow-xl">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Crown className="w-5 h-5 text-yellow-500" />
              Premium Benefits
            </h3>

            <div className="space-y-2">
              {membershipBenefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-700 dark:text-white/80">
                  <Shield className="w-4 h-4 text-yellow-500" />
                  {benefit}
                </div>
              ))}
            </div>

            <Button className="w-full mt-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600">
              Upgrade Membership
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
