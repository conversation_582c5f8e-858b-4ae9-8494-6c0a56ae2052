"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  TrendingUp,
  DollarSign,
  Target,
  Calendar,
  Award,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  Star,
  Clock,
  Users,
  Crown,
  Rocket,
  Gift,
} from "lucide-react"
import { useState } from "react"

export default function NewChallenge() {
  const [selectedAccount, setSelectedAccount] = useState("50k")

  const accountTypes = [
    {
      id: "25k",
      name: "$25,000",
      price: "$155",
      originalPrice: "$299",
      description: "Perfect for beginners",
      features: ["8% profit target", "5% max drawdown", "30 days time limit", "80% profit split"],
      popular: false,
      savings: "48% OFF",
    },
    {
      id: "50k",
      name: "$50,000",
      price: "$250",
      originalPrice: "$499",
      description: "Most popular choice",
      features: ["8% profit target", "5% max drawdown", "30 days time limit", "80% profit split"],
      popular: true,
      savings: "50% OFF",
    },
    {
      id: "100k",
      name: "$100,000",
      price: "$345",
      originalPrice: "$699",
      description: "For experienced traders",
      features: ["8% profit target", "5% max drawdown", "30 days time limit", "80% profit split"],
      popular: false,
      savings: "51% OFF",
    },
    {
      id: "200k",
      name: "$200,000",
      price: "$495",
      originalPrice: "$999",
      description: "Maximum account size",
      features: ["8% profit target", "5% max drawdown", "30 days time limit", "80% profit split"],
      popular: false,
      savings: "50% OFF",
    },
  ]

  const features = [
    {
      icon: Target,
      title: "8% Profit Target",
      description: "Reach 8% profit to pass Phase 1",
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/30",
    },
    {
      icon: Shield,
      title: "5% Max Drawdown",
      description: "Stay within 5% daily drawdown limit",
      color: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
    },
    {
      icon: Calendar,
      title: "30 Days Time Limit",
      description: "Complete Phase 1 within 30 days",
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
    },
    {
      icon: Award,
      title: "80% Profit Split",
      description: "Keep 80% of your profits",
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
    },
  ]

  return (
    <div className="space-y-12">
      {/* Header Section */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Choose Your Challenge</h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Select the perfect account size for your trading journey. All challenges include our comprehensive evaluation process.
        </p>
      </div>

      {/* Sale Banner */}
      <div className="bg-gradient-to-r from-red-500 to-pink-600 text-white p-6 rounded-xl text-center">
        <div className="flex items-center justify-center gap-4 mb-2">
          <Gift className="w-6 h-6" />
          <h2 className="text-2xl font-bold">SPECIAL OFFER - UP TO 51% OFF!</h2>
          <Gift className="w-6 h-6" />
        </div>
        <p className="text-lg">Use coupon code: <span className="font-bold text-yellow-300 text-xl">SAVE70</span></p>
        <p className="text-sm text-red-100 mt-2">Limited time offer - Don't miss out on massive savings!</p>
      </div>

      {/* Account Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {accountTypes.map((account) => (
          <Card
            key={account.id}
            className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 ${
              selectedAccount === account.id
                ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg"
                : "hover:bg-gray-50 dark:hover:bg-gray-800"
            } relative overflow-hidden`}
            onClick={() => setSelectedAccount(account.id)}
          >
            {account.popular && (
              <div className="absolute top-0 right-0 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                MOST POPULAR
              </div>
            )}
            <div className="absolute top-2 left-2">
              <Badge className="bg-red-500 text-white text-xs">{account.savings}</Badge>
            </div>
            
            <CardHeader className="pb-3 pt-8">
              <div className="text-center">
                <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{account.name}</CardTitle>
                <CardDescription className="text-sm">{account.description}</CardDescription>
              </div>
            </CardHeader>
            
            <CardContent className="text-center">
              <div className="mb-4">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">{account.price}</div>
                <div className="text-sm text-gray-500 line-through">{account.originalPrice}</div>
              </div>
              
              <ul className="space-y-3 text-left">
                {account.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              
              <Button 
                className={`w-full mt-6 ${
                  selectedAccount === account.id
                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300"
                }`}
              >
                {selectedAccount === account.id ? "Selected" : "Choose Plan"}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {features.map((feature, index) => {
          const IconComponent = feature.icon
          return (
            <Card key={index} className="text-center hover:shadow-lg transition-all duration-200">
              <CardContent className="pt-6">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full ${feature.bgColor} flex items-center justify-center`}>
                  <IconComponent className={`w-8 h-8 ${feature.color}`} />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2 text-lg">{feature.title}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">{feature.description}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Action Section */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
        <CardContent className="pt-8 pb-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Start Your FxThrone Challenge?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto text-lg">
              Join thousands of successful traders who have passed the FxThrone Challenge and are now trading with funded accounts. 
              Start your journey to financial freedom today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg">
                <Rocket className="w-5 h-5 mr-2" />
                Start Challenge Now
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button variant="outline" className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 px-8 py-4 text-lg">
                <Crown className="w-5 h-5 mr-2" />
                Learn More
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-all duration-200">
          <CardContent className="pt-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
              <Users className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">50,000+</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Active Traders</div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-200">
          <CardContent className="pt-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
              <Award className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">$500M+</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Total Payouts</div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-all duration-200">
          <CardContent className="pt-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
              <Star className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">4.8/5</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Trustpilot Rating</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
